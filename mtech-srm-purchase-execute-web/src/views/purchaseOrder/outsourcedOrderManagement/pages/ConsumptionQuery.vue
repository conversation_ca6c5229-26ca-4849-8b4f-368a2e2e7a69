<!-- 委外原料消耗查询-->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="7adceab8-c7f6-45a4-b902-de3f1ab4aba6"
      search-grid-id="e0a0de05-bf43-4d01-b81f-621c509efa83"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getConsSearchFormItems } from '../config/searchForm'
import { consColumnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: consColumnData,
      toolbar,
      pagination: {
        current: 1,
        size: 20
      }
    }
  },
  computed: {
    searchConditions() {
      const items = getConsSearchFormItems()
      const dateFields = ['createTime', 'itemVoucherDate']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'itemVoucherDate':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      if (field === 'itemVoucherDate') {
        this.searchForm['itemVoucherDateS'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchForm['itemVoucherDateE'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchForm[`${field}S`] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.pagination.current = 1
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.pagination.current = 1
      this.getTableData()
    },
    handlePageChange(pagination) {
      this.pagination = pagination
      this.getTableData()
    },
    async getTableData() {
      const params = {
        ...this.searchForm,
        page: {
          current: this.pagination.current,
          size: this.pagination.size
        }
      }
      console.log(params)
      this.$refs.commonListRef.setLoading(true)
      try {
        const res = await this.$API.outsourcedOrderManagement.pageQueryConsumptionApi(params)
        if (res.code === 200) {
          const pageData = res.data || {}
          const records = pageData.records || []
          this.$refs.commonListRef.setTableData(records)
          this.$refs.commonListRef.setPagination({
            current: pageData.current || 1,
            size: pageData.size || 20,
            total: pageData.total || 0
          })
        } else {
          this.$toast({ content: res?.msg || this.$t('查询失败'), type: 'error' })
          this.$refs.commonListRef.setTableData([])
        }
      } catch (error) {
        console.error('查询委外原料消耗失败:', error)
        this.$toast({ content: error?.msg || this.$t('查询失败'), type: 'error' })
        this.$refs.commonListRef.setTableData([])
      } finally {
        this.$refs.commonListRef.setLoading(false)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res = await this.$API.outsourcedOrderManagement.exportConsumptionApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error?.msg || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
