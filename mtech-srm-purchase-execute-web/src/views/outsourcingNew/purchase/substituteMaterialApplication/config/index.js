import { i18n } from '@/main.js'

// 状态选项
export const statusOptions = [
  { text: i18n.t('待处理'), value: '0' },
  { text: i18n.t('已处理'), value: '1' },
  { text: i18n.t('已退回'), value: '2' }
]

// 同步状态选项
export const syncStatusOptions = [
  { text: i18n.t('未同步'), value: '0' },
  { text: i18n.t('已同步'), value: '1' },
  { text: i18n.t('同步失败'), value: '2' }
]

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'confirm',
    name: i18n.t('批量确认'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'reject',
    name: i18n.t('批量拒绝'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'syncSap',
    name: i18n.t('同步SAP'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

// 列表视图列配置
export const listColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('替代料申请单号'),
    field: 'substituteApplicationNo',
    width: 180,
    cellRender: {
      name: 'VxeButton',
      props: {
        type: 'text',
        status: 'primary'
      },
      events: {
        click: ({ row }) => {
          // 这里需要在组件中绑定跳转函数
          if (window.handleSubstituteApplicationClick) {
            window.handleSubstituteApplicationClick(row)
          }
        }
      }
    }
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    width: 80,
    formatter: ({ cellValue }) => {
      const statusMap = {
        0: i18n.t('待处理'),
        1: i18n.t('已处理'),
        2: i18n.t('已退回')
      }
      return statusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('采购订单号'),
    field: 'purchaseOrderNo',
    width: 150
  },
  {
    title: i18n.t('采购订单行号'),
    field: 'purchaseOrderLineNo',
    width: 120
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    width: 100
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 150
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode',
    width: 120
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName',
    width: 180
  },
  {
    title: i18n.t('同步状态'),
    field: 'syncStatus',
    width: 100,
    formatter: ({ cellValue }) => {
      const syncStatusMap = {
        0: i18n.t('未同步'),
        1: i18n.t('已同步'),
        2: i18n.t('同步失败')
      }
      return syncStatusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('同步接口信息'),
    field: 'syncInterfaceInfo',
    width: 200
  },
  {
    title: i18n.t('来方备注'),
    field: 'buyerRemark',
    width: 200
  },
  {
    title: i18n.t('供方备注'),
    field: 'supplierRemark',
    width: 200
  },
  {
    title: i18n.t('创建人'),
    field: 'createBy',
    width: 100
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 150,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString()
    }
  },
  {
    title: i18n.t('最后更新人'),
    field: 'updateBy',
    width: 100
  },
  {
    title: i18n.t('最后更新时间'),
    field: 'updateTime',
    width: 150,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString()
    }
  }
]

// 明细视图列配置
export const detailColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('替代料申请单号'),
    field: 'substituteApplicationNo',
    width: 180,
    cellRender: {
      name: 'VxeButton',
      props: {
        type: 'text',
        status: 'primary'
      },
      events: {
        click: ({ row }) => {
          // 这里需要在组件中绑定跳转函数
          if (window.handleSubstituteApplicationClick) {
            window.handleSubstituteApplicationClick(row)
          }
        }
      }
    }
  },
  {
    title: i18n.t('行号'),
    field: 'lineNo',
    width: 80
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    width: 80,
    formatter: ({ cellValue }) => {
      const statusMap = {
        0: i18n.t('待处理'),
        1: i18n.t('已处理'),
        2: i18n.t('已退回')
      }
      return statusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('采购订单号'),
    field: 'purchaseOrderNo',
    width: 150
  },
  {
    title: i18n.t('采购订单行号'),
    field: 'purchaseOrderLineNo',
    width: 120
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    width: 100
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 150
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode',
    width: 120
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName',
    width: 180
  },
  {
    title: i18n.t('物料编码'),
    field: 'materialCode',
    width: 120
  },
  {
    title: i18n.t('物料名称'),
    field: 'materialName',
    width: 180
  },
  {
    title: i18n.t('数量'),
    field: 'quantity',
    width: 100
  },
  {
    title: i18n.t('需求日期'),
    field: 'requirementDate',
    width: 120,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleDateString()
    }
  },
  {
    title: i18n.t('库存地点'),
    field: 'storageLocation',
    width: 120
  },
  {
    title: i18n.t('采购组编码'),
    field: 'purchaseGroupCode',
    width: 120
  },
  {
    title: i18n.t('采购组名称'),
    field: 'purchaseGroupName',
    width: 150
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    width: 120
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    width: 150
  },
  {
    title: i18n.t('同步状态'),
    field: 'syncStatus',
    width: 100,
    formatter: ({ cellValue }) => {
      const syncStatusMap = {
        0: i18n.t('未同步'),
        1: i18n.t('已同步'),
        2: i18n.t('同步失败')
      }
      return syncStatusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('同步接口信息'),
    field: 'syncInterfaceInfo',
    width: 200
  },
  {
    title: i18n.t('来方头备注'),
    field: 'buyerHeaderRemark',
    width: 200
  },
  {
    title: i18n.t('供方头备注'),
    field: 'supplierHeaderRemark',
    width: 200
  },
  {
    title: i18n.t('创建人'),
    field: 'createBy',
    width: 100
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 150,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString()
    }
  },
  {
    title: i18n.t('最后更新人'),
    field: 'updateBy',
    width: 100
  },
  {
    title: i18n.t('最后更新时间'),
    field: 'updateTime',
    width: 150,
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString()
    }
  }
]
