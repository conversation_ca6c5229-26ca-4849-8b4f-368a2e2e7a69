<!-- 替代料申请管理-明细视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="e29002b4-a799-4b7d-bc71-3683909372d9"
      search-grid-id="d3f2533d-3d98-4748-9b73-7cef799f7919"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getDetailSearchFormItems } from '../config/searchForm'
import { detailColumnData, detailToolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: detailColumnData,
      toolbar: detailToolbar,
      currentPage: 1,
      pageSize: 50
    }
  },
  computed: {
    searchConditions() {
      const items = getDetailSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  mounted() {
    // 绑定替代料申请单号点击跳转函数
    window.handleSubstituteApplicationClick = this.handleSubstituteApplicationClick
  },
  beforeDestroy() {
    // 清理全局函数
    if (window.handleSubstituteApplicationClick) {
      delete window.handleSubstituteApplicationClick
    }
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm, page, pageSize }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = page || 1
      this.pageSize = pageSize || 50
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.currentPage = 1
      this.getTableData()
    },
    handlePageChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    handlePageSizeChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    async getTableData() {
      // 构建查询参数，根据采方明细接口文档格式
      const params = {
        // 分页参数
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        // 根据接口文档映射搜索条件
        siteCodeList: this.searchForm.siteCode ? [this.searchForm.siteCode] : [],
        replaceItemCode: this.searchForm.substituteApplicationNo || '',
        statusList: this.searchForm.status ? [Number(this.searchForm.status)] : [],
        orderCode: this.searchForm.purchaseOrderNo || '',
        orderLineNo: this.searchForm.purchaseOrderLineNo
          ? Number(this.searchForm.purchaseOrderLineNo)
          : null,
        itemCode: this.searchForm.materialCode || '',
        itemName: this.searchForm.materialName || '',
        buyerOrgCodeList: this.searchForm.purchaseOrgCode ? [this.searchForm.purchaseOrgCode] : [],
        buyerOrgName: this.searchForm.purchaseOrgName || '',
        categoryCode: this.searchForm.categoryCode || '',
        categoryName: this.searchForm.categoryName || '',
        supplierCodeList: this.searchForm.supplierCode ? [this.searchForm.supplierCode] : [],
        supplierName: '',
        warehouse: this.searchForm.storageLocation || '',
        syncSapStatusList: this.searchForm.syncStatus ? [Number(this.searchForm.syncStatus)] : [],
        syncSapMsg: '',
        // 需求时间范围
        demandTimeS: this.searchForm.demandTimeS || null,
        demandTimeE: this.searchForm.demandTimeE || null,
        // 创建和更新时间范围
        createTimeS: this.searchForm.createTimeS || null,
        createTimeE: this.searchForm.createTimeE || null,
        updateTimeS: this.searchForm.updateTimeS || null,
        updateTimeE: this.searchForm.updateTimeE || null
      }

      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getSubstituteMaterialApplicationDetailApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        // 构建导出参数，与查询参数保持一致
        const params = {
          // 根据接口文档映射搜索条件
          siteCodeList: this.searchForm.siteCode ? [this.searchForm.siteCode] : [],
          replaceItemCode: this.searchForm.substituteApplicationNo || '',
          statusList: this.searchForm.status ? [Number(this.searchForm.status)] : [],
          orderCode: this.searchForm.purchaseOrderNo || '',
          orderLineNo: this.searchForm.purchaseOrderLineNo
            ? Number(this.searchForm.purchaseOrderLineNo)
            : null,
          itemCode: this.searchForm.materialCode || '',
          itemName: this.searchForm.materialName || '',
          buyerOrgCodeList: this.searchForm.purchaseOrgCode
            ? [this.searchForm.purchaseOrgCode]
            : [],
          buyerOrgName: this.searchForm.purchaseOrgName || '',
          categoryCode: this.searchForm.categoryCode || '',
          categoryName: this.searchForm.categoryName || '',
          supplierCodeList: this.searchForm.supplierCode ? [this.searchForm.supplierCode] : [],
          supplierName: '',
          warehouse: this.searchForm.storageLocation || '',
          syncSapStatusList: this.searchForm.syncStatus ? [Number(this.searchForm.syncStatus)] : [],
          syncSapMsg: '',
          // 需求时间范围
          demandTimeS: this.searchForm.demandTimeS || null,
          demandTimeE: this.searchForm.demandTimeE || null,
          // 创建和更新时间范围
          createTimeS: this.searchForm.createTimeS || null,
          createTimeE: this.searchForm.createTimeE || null,
          updateTimeS: this.searchForm.updateTimeS || null,
          updateTimeE: this.searchForm.updateTimeE || null
        }

        const res = await this.$API.outsourcingNew.exportSubstituteMaterialApplicationDetailApi(
          params
        )
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    /**
     * 处理替代料申请单号点击跳转
     */
    handleSubstituteApplicationClick(row) {
      this.$router.push({
        path: '/purchase-execute/substitute-material-application-detail',
        query: {
          id: row.id,
          type: 'view'
        }
      })
    }
  }
}
</script>
