<!-- 替代料申请管理-列表视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="f4a26b0c-abec-4a16-b128-a7391ab59d33"
      search-grid-id="989c961a-0f4d-408b-ac84-930928cd2794"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getListSearchFormItems } from '../config/searchForm'
import { listColumnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: listColumnData,
      toolbar,
      currentPage: 1,
      pageSize: 50
    }
  },
  computed: {
    searchConditions() {
      const items = getListSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  mounted() {
    // 绑定替代料申请单号点击跳转函数
    window.handleSubstituteApplicationClick = this.handleSubstituteApplicationClick
  },
  beforeDestroy() {
    // 清理全局函数
    if (window.handleSubstituteApplicationClick) {
      delete window.handleSubstituteApplicationClick
    }
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm, page, pageSize }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = page || 1
      this.pageSize = pageSize || 50
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.currentPage = 1
      this.getTableData()
    },
    handlePageChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    handlePageSizeChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    async getTableData() {
      // 构建查询参数，根据采方接口文档格式
      const params = {
        // 分页参数
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        // 根据接口文档映射搜索条件
        siteCodeList: this.searchForm.siteCode ? [this.searchForm.siteCode] : [],
        replaceItemCode: this.searchForm.substituteApplicationNo || '',
        statusList: this.searchForm.status ? [Number(this.searchForm.status)] : [],
        orderCode: this.searchForm.purchaseOrderNo || '',
        orderLineNo: this.searchForm.purchaseOrderLineNo
          ? Number(this.searchForm.purchaseOrderLineNo)
          : null,
        supplierCodeList: this.searchForm.supplierCode ? [this.searchForm.supplierCode] : [],
        supplierName: '',
        syncSapStatusList: this.searchForm.syncStatus ? [Number(this.searchForm.syncStatus)] : [],
        syncSapMsg: '',
        // 时间范围参数
        createTimeS: this.searchForm.createTimeS || null,
        createTimeE: this.searchForm.createTimeE || null,
        updateTimeS: this.searchForm.updateTimeS || null,
        updateTimeE: this.searchForm.updateTimeE || null
      }

      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getSubstituteMaterialApplicationListApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        confirm: () => this.handleConfirm(item),
        reject: () => this.handleReject(item),
        syncSap: () => this.handleSyncSap(item),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        // 构建导出参数，与查询参数保持一致
        const params = {
          // 根据接口文档映射搜索条件
          siteCodeList: this.searchForm.siteCode ? [this.searchForm.siteCode] : [],
          replaceItemCode: this.searchForm.substituteApplicationNo || '',
          statusList: this.searchForm.status ? [Number(this.searchForm.status)] : [],
          orderCode: this.searchForm.purchaseOrderNo || '',
          orderLineNo: this.searchForm.purchaseOrderLineNo
            ? Number(this.searchForm.purchaseOrderLineNo)
            : null,
          supplierCodeList: this.searchForm.supplierCode ? [this.searchForm.supplierCode] : [],
          supplierName: '',
          syncSapStatusList: this.searchForm.syncStatus ? [Number(this.searchForm.syncStatus)] : [],
          syncSapMsg: '',
          // 时间范围参数
          createTimeS: this.searchForm.createTimeS || null,
          createTimeE: this.searchForm.createTimeE || null,
          updateTimeS: this.searchForm.updateTimeS || null,
          updateTimeE: this.searchForm.updateTimeE || null
        }

        const res = await this.$API.outsourcingNew.exportSubstituteMaterialApplicationListApi(
          params
        )
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    /**
     * 批量确认 - 只能批量选择待采购确认的数据，确认后自动同步SAP，状态更新为已确认，若同步失败，则提示报错，更新同步状态和同步接口信息，同时不能更新状态
     */
    async handleConfirm(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要确认的记录'), type: 'warning' })
        return
      }

      // 检查状态：仅支持待采购确认状态(20)的数据
      const invalidRecords = selectedRecords.filter((record) => record.status !== '20')
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能确认待采购确认状态的数据'),
          type: 'warning'
        })
        return
      }

      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认以下记录吗？\n{0}', [recordNames])
        },
        success: async () => {
          try {
            item.loading = true
            const ids = selectedRecords.map((record) => record.id)
            const res = await this.$API.outsourcingNew.batchConfirmSubstituteMaterialApplicationApi(
              {
                ids
              }
            )

            if (res.code === 200) {
              this.$toast({ content: this.$t('确认成功'), type: 'success' })
              this.getTableData() // 刷新列表
            } else {
              this.$toast({ content: res.msg || this.$t('确认失败'), type: 'warning' })
            }
          } catch (error) {
            console.error('确认失败:', error)
            this.$toast({ content: this.$t('确认失败'), type: 'error' })
          } finally {
            item.loading = false
          }
        }
      })
    },

    /**
     * 批量拒绝 - 只能拒绝待采购确认的数据，需弹窗要求一定录入"拒绝原因"，拒绝后状态更新为采购已拒绝，同时拒绝原因更新到采方备注
     */
    async handleReject(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要拒绝的记录'), type: 'warning' })
        return
      }

      // 检查状态：仅支持待采购确认状态(20)的数据
      const invalidRecords = selectedRecords.filter((record) => record.status !== '20')
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能拒绝待采购确认状态的数据'),
          type: 'warning'
        })
        return
      }

      // 显示拒绝原因输入对话框
      this.showRejectDialog(selectedRecords, item)
    },

    /**
     * 显示拒绝原因输入对话框
     */
    showRejectDialog(selectedRecords, item) {
      // 使用prompt方式获取拒绝原因
      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')

      this.$dialog({
        data: {
          title: this.$t('拒绝'),
          message: this.$t('确认拒绝以下记录吗？\n{0}\n\n请在下方输入拒绝原因：', [recordNames]),
          showInput: true,
          inputPlaceholder: this.$t('请输入拒绝原因'),
          inputType: 'textarea',
          inputValidator: (value) => {
            if (!value || !value.trim()) {
              return this.$t('请输入拒绝原因')
            }
            return true
          }
        },
        success: (value) => {
          const rejectReason = value.trim()
          if (rejectReason) {
            this.executeReject(selectedRecords, rejectReason, item)
          }
        }
      })
    },

    /**
     * 执行拒绝操作
     */
    async executeReject(selectedRecords, rejectReason, item) {
      try {
        item.loading = true
        const ids = selectedRecords.map((record) => record.id)
        const res = await this.$API.outsourcingNew.batchRejectSubstituteMaterialApplicationApi({
          ids,
          rejectReason
        })

        if (res.code === 200) {
          this.$toast({ content: this.$t('拒绝成功'), type: 'success' })
          this.getTableData() // 刷新列表
        } else {
          this.$toast({ content: res.msg || this.$t('拒绝失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('拒绝失败:', error)
        this.$toast({ content: this.$t('拒绝失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    /**
     * 同步SAP系统 - 只要是状态为已确认的都能同步，即使之前已经同步过
     */
    async handleSyncSap(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要同步的记录'), type: 'warning' })
        return
      }

      // 检查状态：仅支持已确认状态(30)的数据
      const invalidRecords = selectedRecords.filter((record) => record.status !== '30')
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能同步已确认状态的数据'),
          type: 'warning'
        })
        return
      }

      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认同步以下记录到SAP系统吗？\n{0}', [recordNames])
        },
        success: async () => {
          try {
            item.loading = true
            const ids = selectedRecords.map((record) => record.id)
            const res = await this.$API.outsourcingNew.syncSapSubstituteMaterialApplicationApi({
              ids
            })

            if (res.code === 200) {
              this.$toast({ content: this.$t('同步成功'), type: 'success' })
              this.getTableData() // 刷新列表
            } else {
              this.$toast({ content: res.msg || this.$t('同步失败'), type: 'warning' })
            }
          } catch (error) {
            console.error('同步失败:', error)
            this.$toast({ content: this.$t('同步失败'), type: 'error' })
          } finally {
            item.loading = false
          }
        }
      })
    },

    /**
     * 处理替代料申请单号点击跳转
     */
    handleSubstituteApplicationClick(row) {
      this.$router.push({
        path: '/purchase-execute/substitute-material-application-detail',
        query: {
          id: row.id,
          type: 'view'
        }
      })
    }
  }
}
</script>
