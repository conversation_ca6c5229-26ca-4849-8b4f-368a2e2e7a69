/**
 * 供方-替代料申请管理搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions, syncStatusOptions } from './index'

// 列表视图搜索表单配置
export const getListSearchFormItems = () => [
  {
    label: i18n.t('工厂编码'),
    field: 'siteCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode'],
      multiple: true
    }
  },
  {
    label: i18n.t('替代料申请单号'),
    field: 'replaceItemCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入替代料申请单号')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'statusList',
    type: 'select',
    options: statusOptions,
    props: {
      multiple: true
    }
  },
  {
    label: i18n.t('订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入订单号')
    }
  },
  {
    label: i18n.t('订单行号'),
    field: 'orderLineNo',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入订单行号')
    }
  },
  {
    label: i18n.t('同步SAP状态'),
    field: 'syncSapStatusList',
    type: 'select',
    options: syncStatusOptions,
    props: {
      multiple: true
    }
  },
  {
    label: i18n.t('同步SAP消息'),
    field: 'syncSapMsg',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入同步SAP消息')
    }
  }
]

// 明细视图搜索表单配置
export const getDetailSearchFormItems = () => [
  {
    label: i18n.t('工厂编码'),
    field: 'siteCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode'],
      multiple: true
    }
  },
  {
    label: i18n.t('替代料申请单号'),
    field: 'replaceItemCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入替代料申请单号')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'statusList',
    type: 'select',
    options: statusOptions,
    props: {
      multiple: true
    }
  },
  {
    label: i18n.t('订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入订单号')
    }
  },
  {
    label: i18n.t('订单行号'),
    field: 'orderLineNo',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入订单行号')
    }
  },
  {
    label: i18n.t('物料编码'),
    field: 'itemCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入物料编码')
    }
  },
  {
    label: i18n.t('物料名称'),
    field: 'itemName',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入物料名称')
    }
  },
  {
    label: i18n.t('采购组织编码'),
    field: 'buyerOrgCodeList',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/organization/paged-query`,
      fields: { text: 'organizationName', value: 'organizationCode' },
      searchFields: ['organizationName', 'organizationCode'],
      multiple: true
    }
  },
  {
    label: i18n.t('采购组织名称'),
    field: 'buyerOrgName',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入采购组织名称')
    }
  },
  {
    label: i18n.t('品类编码'),
    field: 'categoryCode',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入品类编码')
    }
  },
  {
    label: i18n.t('品类名称'),
    field: 'categoryName',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入品类名称')
    }
  },
  {
    label: i18n.t('库存地点'),
    field: 'warehouse',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入库存地点')
    }
  },
  {
    label: i18n.t('同步SAP状态'),
    field: 'syncSapStatusList',
    type: 'select',
    options: syncStatusOptions,
    props: {
      multiple: true
    }
  },
  {
    label: i18n.t('同步SAP消息'),
    field: 'syncSapMsg',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入同步SAP消息')
    }
  },
  {
    label: i18n.t('需求时间'),
    field: 'demandTime',
    type: 'dateRange'
  }
]
