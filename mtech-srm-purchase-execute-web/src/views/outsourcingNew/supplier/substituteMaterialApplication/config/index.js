import { i18n } from '@/main.js'

// 状态选项
export const statusOptions = [
  { text: i18n.t('草稿'), value: '10' },
  { text: i18n.t('已提交'), value: '20' },
  { text: i18n.t('已审批'), value: '30' },
  { text: i18n.t('已拒绝'), value: '40' },
  { text: i18n.t('已取消'), value: '50' }
]

// 同步状态选项
export const syncStatusOptions = [
  { text: i18n.t('未同步'), value: '0' },
  { text: i18n.t('已同步'), value: '1' },
  { text: i18n.t('同步失败'), value: '2' }
]

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'add',
    name: i18n.t('新增'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'edit',
    name: i18n.t('编辑'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'submit',
    name: i18n.t('提交'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'cancel',
    name: i18n.t('取消'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    icon: '',
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

// 列表视图列配置
export const listColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('替代料申请单号'),
    field: 'replaceItemCode',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    width: 100,
    align: 'center',
    formatter: ({ cellValue }) => {
      const statusMap = {
        10: i18n.t('草稿'),
        20: i18n.t('已提交'),
        30: i18n.t('已审批'),
        40: i18n.t('已拒绝'),
        50: i18n.t('已取消')
      }
      return statusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('订单号'),
    field: 'orderCode',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('订单行号'),
    field: 'orderLineNo',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('工厂编码'),
    field: 'siteCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('同步SAP状态'),
    field: 'syncSapStatus',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      const syncStatusMap = {
        0: i18n.t('未同步'),
        1: i18n.t('已同步'),
        2: i18n.t('同步失败')
      }
      return syncStatusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('同步SAP消息'),
    field: 'syncSapMsg',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('来方备注'),
    field: 'buyerRemark',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('供方备注'),
    field: 'supplierRemark',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('创建人'),
    field: 'createBy',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 160,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  },
  {
    title: i18n.t('最后更新人'),
    field: 'updateBy',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('最后更新时间'),
    field: 'updateTime',
    width: 160,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
]

// 明细视图列配置
export const detailColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('替代料申请单号'),
    field: 'replaceItemCode',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('行号'),
    field: 'lineNo',
    width: 80,
    align: 'center'
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    width: 100,
    align: 'center',
    formatter: ({ cellValue }) => {
      const statusMap = {
        0: i18n.t('新建'),
        1: i18n.t('待采购确认'),
        2: i18n.t('已确认'),
        3: i18n.t('采购已拒绝'),
        4: i18n.t('已取消')
      }
      return statusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('订单号'),
    field: 'orderCode',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('订单行号'),
    field: 'orderLineNo',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('工厂编码'),
    field: 'siteCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('数量'),
    field: 'qty',
    width: 100,
    align: 'center',
    formatter: ({ cellValue }) => {
      return cellValue ? Number(cellValue).toFixed(2) : '0.00'
    }
  },
  {
    title: i18n.t('需求时间'),
    field: 'demandTime',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleDateString('zh-CN')
    }
  },
  {
    title: i18n.t('库存地点'),
    field: 'warehouse',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('采购组织编码'),
    field: 'buyerOrgCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('采购组织名称'),
    field: 'buyerOrgName',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    width: 150,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('同步SAP状态'),
    field: 'syncSapStatus',
    width: 120,
    align: 'center',
    formatter: ({ cellValue }) => {
      const syncStatusMap = {
        0: i18n.t('未同步'),
        1: i18n.t('同步中'),
        2: i18n.t('同步成功'),
        3: i18n.t('同步失败')
      }
      return syncStatusMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('同步SAP消息'),
    field: 'syncSapMsg',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('采方备注'),
    field: 'buyerRemark',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('供方备注'),
    field: 'supplierRemark',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 160,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  },
  {
    title: i18n.t('最后更新人'),
    field: 'updateUserName',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('最后更新时间'),
    field: 'updateTime',
    width: 160,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
]
