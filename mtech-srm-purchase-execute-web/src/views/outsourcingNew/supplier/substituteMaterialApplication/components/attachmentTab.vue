<!-- 附件信息 -->
<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      :row-config="{ height: 45 }"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'AttachmentTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      fileTypeIconMap: {
        pdf: 'vxe-icon-file-pdf',
        doc: 'vxe-icon-file-word',
        docx: 'vxe-icon-file-word',
        xls: 'vxe-icon-file-excel',
        xlsx: 'vxe-icon-file-excel',
        ppt: 'vxe-icon-file-ppt',
        pptx: 'vxe-icon-file-ppt',
        txt: 'vxe-icon-file-txt',
        jpg: 'vxe-icon-file-image',
        jpeg: 'vxe-icon-file-image',
        png: 'vxe-icon-file-image',
        gif: 'vxe-icon-file-image',
        zip: 'vxe-icon-file-zip',
        rar: 'vxe-icon-file-zip'
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'detail'
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'fileName',
          title: this.$t('文件名'),
          width: 300,
          showOverflow: 'tooltip',
          slots: {
            default: ({ row }) => {
              return [
                <div class='file-cell'>
                  <div class='file-info'>
                    <i
                      class={this.getFileTypeIcon(row.fileType)}
                      style='margin-right: 8px; color: #409eff;'></i>
                    <a
                      class='file-name-link'
                      onClick={() => this.handlePreviewFile(row)}
                      title={row.fileName}>
                      {row.fileName}
                    </a>
                  </div>
                  <div class='file-actions'>
                    <a class='action-btn' onClick={() => this.handleDownloadFile(row)}>
                      <i class='vxe-icon-download'></i>
                      {this.$t('下载')}
                    </a>
                    {this.editable && (
                      <a class='action-btn delete-btn' onClick={() => this.handleDeleteFile([row])}>
                        <i class='vxe-icon-delete'></i>
                        {this.$t('删除')}
                      </a>
                    )}
                  </div>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileType',
          title: this.$t('文件类型'),
          width: 120,
          align: 'center',
          slots: {
            default: ({ row }) => {
              return [
                <div class='file-type-cell'>
                  <i
                    class={this.getFileTypeIcon(row.fileType)}
                    style='margin-right: 4px; color: #409eff;'></i>
                  <span>{row.fileType?.toUpperCase() || '-'}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小'),
          width: 120,
          align: 'center',
          formatter: ({ cellValue }) => {
            return this.formatFileSize(cellValue)
          }
        },
        {
          field: 'creator',
          title: this.$t('创建人'),
          width: 120,
          align: 'center'
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          width: 160,
          align: 'center',
          formatter: ({ cellValue }) => {
            return cellValue ? this.$utils.formatDate(cellValue, 'YYYY-MM-DD HH:mm:ss') : ''
          }
        }
      ]
    },
    toolbar() {
      let btns = []
      if (this.editable) {
        btns = [
          { code: 'upload', name: this.$t('上传'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'download', name: this.$t('下载'), status: 'info', loading: false }
        ]
      } else {
        btns = [{ code: 'download', name: this.$t('下载'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    this.initTableData()
  },
  methods: {
    /**
     * 初始化表格数据
     */
    initTableData() {
      if (this.pageType !== 'create') {
        this.getTableData()
      } else {
        // 创建模式下使用传入的数据
        this.$emit('updateDetail', this.tableData)
      }
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        // TODO: 调用实际的API获取附件数据
        // const params = {
        //   docId: this.$route.query?.id,
        //   docType: 'substitute_material'
        // }
        // const res = await this.$API.substituteMaterial.getAttachmentList(params)
        // if (res.code === 200) {
        //   this.tableData = res.data || []
        //   this.$emit('updateDetail', this.tableData)
        // }

        // 临时使用传入的数据
        this.$emit('updateDetail', this.tableData)
      } catch (error) {
        console.error('获取附件数据失败:', error)
        this.$toast({ content: this.$t('获取附件数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar(item) {
      switch (item.code) {
        case 'upload':
          this.handleUploadFile()
          break
        case 'delete':
          this.handleBatchDelete()
          break
        case 'download':
          this.handleBatchDownload()
          break
        default:
          break
      }
    },

    /**
     * 上传文件
     */
    handleUploadFile() {
      this.$dialog({
        modal: () => import('@/components/Upload/index.vue'),
        data: {
          title: this.$t('上传附件'),
          multiple: true,
          accept: '*',
          maxSize: 50 * 1024 * 1024 // 50MB
        },
        success: async (data) => {
          try {
            const { id, fileName, fileSize, fileType, url } = data
            const userInfo = this.$store.state.user?.userInfo || {}

            // 构建附件数据
            const attachmentData = {
              id: `attachment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              sysFileId: id,
              fileName: fileName,
              fileType: fileType,
              fileSize: fileSize,
              url: url,
              creator: userInfo.userName || 'system',
              createTime: new Date().getTime()
            }

            // TODO: 调用API保存附件信息
            // const params = {
            //   docId: this.$route.query?.id,
            //   docType: 'substitute_material',
            //   ...attachmentData
            // }
            // const res = await this.$API.substituteMaterial.saveAttachment(params)
            // if (res.code === 200) {
            //   this.$toast({ content: this.$t('上传成功'), type: 'success' })
            //   this.getTableData()
            // }

            // 临时添加到本地数据
            this.tableRef.insertAt(attachmentData, -1)
            this.updateTableData()
            this.$toast({ content: this.$t('上传成功'), type: 'success' })
          } catch (error) {
            console.error('上传文件失败:', error)
            this.$toast({ content: this.$t('上传文件失败'), type: 'error' })
          }
        }
      })
    },

    /**
     * 批量删除
     */
    handleBatchDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要删除的文件'), type: 'warning' })
        return
      }
      this.handleDeleteFile(selectedRecords)
    },

    /**
     * 删除文件
     */
    handleDeleteFile(files) {
      const fileNames = files.map((file) => file.fileName).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除文件：{0}？', [fileNames])
        },
        success: async () => {
          try {
            // TODO: 调用API删除附件
            // const ids = files.map(file => file.id)
            // const res = await this.$API.substituteMaterial.deleteAttachment({ ids })
            // if (res.code === 200) {
            //   this.$toast({ content: this.$t('删除成功'), type: 'success' })
            //   this.getTableData()
            // }

            // 临时从本地数据删除
            files.forEach((file) => {
              this.tableRef.remove(file)
            })
            this.updateTableData()
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
          } catch (error) {
            console.error('删除文件失败:', error)
            this.$toast({ content: this.$t('删除文件失败'), type: 'error' })
          }
        }
      })
    },

    /**
     * 批量下载
     */
    handleBatchDownload() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要下载的文件'), type: 'warning' })
        return
      }
      selectedRecords.forEach((file) => {
        this.handleDownloadFile(file)
      })
    },

    /**
     * 下载文件
     */
    async handleDownloadFile(file) {
      try {
        // TODO: 调用API下载文件
        // const res = await this.$API.fileService.downloadPublicFile({ id: file.sysFileId })
        // download({ fileName: file.fileName, blob: new Blob([res.data]) })

        // 临时使用文件URL下载
        if (file.url) {
          const link = document.createElement('a')
          link.href = file.url
          link.download = file.fileName
          link.click()
        } else {
          this.$toast({ content: this.$t('文件下载链接不存在'), type: 'warning' })
        }
      } catch (error) {
        console.error('下载文件失败:', error)
        this.$toast({ content: this.$t('下载文件失败'), type: 'error' })
      }
    },

    /**
     * 预览文件
     */
    async handlePreviewFile(file) {
      try {
        // TODO: 调用API获取预览链接
        // const params = {
        //   id: file.sysFileId,
        //   useType: 2
        // }
        // const res = await this.$API.fileService.filePreview(params)
        // if (res.code === 200) {
        //   window.open(res.data)
        // }

        // 临时使用文件URL预览
        if (file.url) {
          window.open(file.url)
        } else {
          this.$toast({ content: this.$t('文件预览链接不存在'), type: 'warning' })
        }
      } catch (error) {
        console.error('预览文件失败:', error)
        this.$toast({ content: this.$t('预览文件失败'), type: 'error' })
      }
    },

    /**
     * 获取文件类型图标
     */
    getFileTypeIcon(fileType) {
      if (!fileType) return 'vxe-icon-file'
      const type = fileType.toLowerCase()
      return this.fileTypeIconMap[type] || 'vxe-icon-file'
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(size) {
      if (!size || size === 0) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      let fileSize = parseInt(size)

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }

      return `${fileSize.toFixed(2)} ${units[index]}`
    },

    /**
     * 更新表格数据
     */
    updateTableData() {
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 获取表格数据用于保存
     */
    getTableDataForSave() {
      const tableData = this.tableRef.getTableData().visibleData
      return tableData.map((row) => ({
        ...row,
        id: row.id.includes('attachment_') ? null : row.id, // 新增附件的临时ID设为null
        createTime: row.createTime ? new Date(row.createTime).getTime() : null
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.file-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .file-info {
    display: flex;
    align-items: center;

    .file-name-link {
      color: #409eff;
      cursor: pointer;
      text-decoration: none;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .file-actions {
    display: flex;
    gap: 12px;

    .action-btn {
      color: #409eff;
      font-size: 12px;
      cursor: pointer;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 4px;

      &:hover {
        text-decoration: underline;
      }

      &.delete-btn {
        color: #f56c6c;
      }
    }
  }
}

.file-type-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.vxe-table) {
  .vxe-body--row {
    &:hover {
      background-color: #f5f7fa;
    }
  }

  .vxe-cell {
    padding: 8px;
  }
}
</style>
