<!-- 物料信息 -->
<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      editRules: {
        materialCode: [{ required: true, message: this.$t('物料编码为必填项') }],
        quantity: [{ required: true, message: this.$t('数量为必填项') }],
        demandDate: [{ required: true, message: this.$t('需求日期为必填项') }],
        creator: [{ required: true, message: this.$t('创建人为必填项') }],
        createTime: [{ required: true, message: this.$t('创建时间为必填项') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'detail'
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('行号'),
          width: 80,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          width: 150,
          editRender: this.editable ? {} : null,
          slots: this.editable
            ? {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.materialCode}
                      placeholder={this.$t('请输入物料编码')}
                      transfer
                      clearable
                      onBlur={() => this.handleMaterialCodeChange(row)}
                    />
                  ]
                }
              }
            : null
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          width: 200,
          showOverflow: 'tooltip'
        },
        {
          field: 'unit',
          title: this.$t('单位'),
          width: 100
        },
        {
          field: 'quantity',
          title: this.$t('数量'),
          width: 120,
          editRender: this.editable ? {} : null,
          slots: this.editable
            ? {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      type='number'
                      v-model={row.quantity}
                      placeholder={this.$t('请输入数量')}
                      min='0'
                      transfer
                      clearable
                    />
                  ]
                }
              }
            : null
        },
        {
          field: 'demandDate',
          title: this.$t('需求日期'),
          width: 150,
          editRender: this.editable ? {} : null,
          slots: this.editable
            ? {
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      type='date'
                      v-model={row.demandDate}
                      placeholder={this.$t('请选择需求日期')}
                      transfer
                      clearable
                    />
                  ]
                }
              }
            : null,
          formatter: ({ cellValue }) => {
            return cellValue ? this.$utils.formatDate(cellValue, 'YYYY-MM-DD') : ''
          }
        },
        {
          field: 'storageLocation',
          title: this.$t('库存地点'),
          width: 150,
          showOverflow: 'tooltip'
        },
        {
          field: 'purchaseGroupCode',
          title: this.$t('采购组编码'),
          width: 120
        },
        {
          field: 'purchaseGroupName',
          title: this.$t('采购组名称'),
          width: 150,
          showOverflow: 'tooltip'
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          width: 120
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          width: 150,
          showOverflow: 'tooltip'
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          width: 200,
          editRender: this.editable ? {} : null,
          slots: this.editable
            ? {
                edit: ({ row }) => {
                  return [
                    <vxe-textarea
                      v-model={row.remark}
                      placeholder={this.$t('请输入备注')}
                      maxlength='1000'
                      transfer
                      clearable
                    />
                  ]
                }
              }
            : null,
          showOverflow: 'tooltip'
        },
        {
          field: 'creator',
          title: this.$t('创建人'),
          width: 120
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          width: 160,
          formatter: ({ cellValue }) => {
            return cellValue ? this.$utils.formatDate(cellValue, 'YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          field: 'lastUpdater',
          title: this.$t('最后更新人'),
          width: 120
        },
        {
          field: 'lastUpdateTime',
          title: this.$t('最后更新时间'),
          width: 160,
          formatter: ({ cellValue }) => {
            return cellValue ? this.$utils.formatDate(cellValue, 'YYYY-MM-DD HH:mm:ss') : ''
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (this.editable) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  mounted() {
    this.initTableData()
  },
  methods: {
    /**
     * 初始化表格数据
     */
    initTableData() {
      if (this.pageType === 'create') {
        // 创建模式下初始化空数据
        this.addNewRow()
      } else {
        // 详情/编辑模式下获取数据
        this.getTableData()
      }
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        // TODO: 调用实际的API获取物料信息数据
        // const params = {
        //   id: this.$route.query?.id
        // }
        // const res = await this.$API.substituteMaterial.getMaterialList(params)
        // if (res.code === 200) {
        //   this.tableData = res.data || []
        //   this.$emit('updateDetail', this.tableData)
        // }

        // 临时模拟数据
        this.$emit('updateDetail', this.tableData)
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'getMaterialInfo':
          this.handleGetMaterialInfo()
          break
        default:
          break
      }
    },

    /**
     * 新增行
     */
    handleAdd() {
      this.addNewRow()
    },

    /**
     * 添加新行
     */
    addNewRow() {
      const newRow = {
        id: `row_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        materialCode: '',
        materialName: '',
        unit: '',
        quantity: '',
        demandDate: '',
        storageLocation: '',
        purchaseGroupCode: '',
        purchaseGroupName: '',
        categoryCode: '',
        categoryName: '',
        remark: '',
        creator: this.$store.state.user?.userInfo?.userName || 'system',
        createTime: new Date().getTime(),
        lastUpdater: '',
        lastUpdateTime: ''
      }

      this.tableRef.insertAt(newRow, -1)
      this.updateTableData()
    },

    /**
     * 删除行
     */
    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要删除的行'), type: 'warning' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的记录吗？')
        },
        success: () => {
          this.tableRef.removeCheckboxRow()
          this.updateTableData()
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
        }
      })
    },

    /**
     * 获取物料信息
     */
    async handleGetMaterialInfo() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要获取物料信息的行'), type: 'warning' })
        return
      }

      try {
        this.loading = true
        for (const row of selectedRecords) {
          if (row.materialCode) {
            await this.fetchMaterialInfo(row)
          }
        }
        this.updateTableData()
        this.$toast({ content: this.$t('获取物料信息成功'), type: 'success' })
      } catch (error) {
        console.error('获取物料信息失败:', error)
        this.$toast({ content: this.$t('获取物料信息失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取单个物料信息
     */
    async fetchMaterialInfo(row) {
      try {
        // TODO: 调用SAP接口获取物料信息
        // const res = await this.$API.masterData.getMaterialInfo({ materialCode: row.materialCode })
        // if (res.code === 200 && res.data) {
        //   const materialInfo = res.data
        //   row.materialName = materialInfo.materialName || ''
        //   row.unit = materialInfo.unit || ''
        //   row.storageLocation = materialInfo.storageLocation || ''
        //   row.purchaseGroupCode = materialInfo.purchaseGroupCode || ''
        //   row.purchaseGroupName = materialInfo.purchaseGroupName || ''
        //   row.categoryCode = materialInfo.categoryCode || ''
        //   row.categoryName = materialInfo.categoryName || ''
        // }

        // 临时模拟数据
        row.materialName = `物料名称_${row.materialCode}`
        row.unit = 'PC'
        row.storageLocation = '1000'
        row.purchaseGroupCode = 'PG001'
        row.purchaseGroupName = '采购组001'
        row.categoryCode = 'CAT001'
        row.categoryName = '品类001'
      } catch (error) {
        console.error('获取物料信息失败:', error)
        throw error
      }
    },

    /**
     * 处理物料编码变化
     */
    async handleMaterialCodeChange(row) {
      if (row.materialCode && row.materialCode.trim()) {
        try {
          await this.fetchMaterialInfo(row)
          this.updateTableData()
        } catch (error) {
          console.error('获取物料信息失败:', error)
        }
      }
    },

    /**
     * 更新表格数据
     */
    updateTableData() {
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 更新最后修改信息
        const { row } = args
        if (row) {
          row.lastUpdater = this.$store.state.user?.userInfo?.userName || 'system'
          row.lastUpdateTime = new Date().getTime()
        }
        this.updateTableData()
      }
    },

    /**
     * 验证表格数据
     */
    validateTableData() {
      const tableData = this.tableRef.getTableData().visibleData
      const errors = []

      tableData.forEach((row, index) => {
        if (!row.materialCode) {
          errors.push(`第${index + 1}行：物料编码不能为空`)
        }
        if (!row.quantity || row.quantity <= 0) {
          errors.push(`第${index + 1}行：数量必须大于0`)
        }
        if (!row.demandDate) {
          errors.push(`第${index + 1}行：需求日期不能为空`)
        }
      })

      return {
        valid: errors.length === 0,
        errors
      }
    },

    /**
     * 获取表格数据用于保存
     */
    getTableDataForSave() {
      const validation = this.validateTableData()
      if (!validation.valid) {
        this.$toast({
          content: validation.errors.join('\n'),
          type: 'warning'
        })
        return null
      }

      return this.tableRef.getTableData().visibleData.map((row) => ({
        ...row,
        id: row.id.includes('row_') ? null : row.id, // 新增行的临时ID设为null
        quantity: Number(row.quantity),
        demandDate: row.demandDate ? new Date(row.demandDate).getTime() : null
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }

    .vxe-cell {
      padding: 4px 8px;
    }
  }
}
</style>
