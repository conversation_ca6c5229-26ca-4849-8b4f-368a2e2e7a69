<!-- 供方-替代料申请管理-详情页 -->
<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="top-title">
            <span>{{ $t('状态') }}：{{ formData.status || '-' }}</span>
            <span>{{ $t('替代料申请单号') }}：{{ formData.stockInCode || '-' }}</span>
            <span>{{ $t('创建人') }}：{{ formData.createUserName || '-' }}</span>
            <span>{{ $t('创建时间') }}：{{ formData.createTime || '-' }}</span>
            <span>{{ $t('更新人') }}：{{ formData.createUserName || '-' }}</span>
            <span>{{ $t('更新时间') }}：{{ formData.createTime || '-' }}</span>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in toolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="formRef" :model="formData" :rules="formRules">
            <mt-form-item
              v-for="(item, key) in filteredFormItems"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              label-style="top"
            >
              <component
                :is="item.component"
                v-model="formData[item.prop]"
                v-bind="getComponentProps(item)"
                @change="(e) => handleFieldChange(item.prop, e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="tabsRef"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="formData"
          :table-data="currentTableData"
          @updateDetail="updateDetail"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { shippingMethodOptions, warehouseTypeOptions } from './config'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'

export default {
  name: 'InventoryManagementDetail',
  components: {
    VxeRemoteSearch
  },
  data() {
    const dateDisabled = (params) => {
      const { date } = params
      return date < dayjs().startOf('day')
    }
    // 车牌号验证
    const validateCarNo = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (
        !/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/.test(
          value
        )
      ) {
        callback(new Error(this.$t('请输入正确的车牌号')))
      } else {
        callback()
      }
    }
    return {
      isExpand: true,
      formData: {},
      activeTabIndex: 0,
      keepArr: ['ItemTab', 'SnTab', 'DriverTab'],
      tableData: [],
      itemList: [],
      attachmentList: [],
      loading: false,
      shippingMethodOptions,
      warehouseTypeOptions,
      dateDisabled,
      validateCarNo
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'create'
    },
    filteredFormItems() {
      return this.formItems.filter((item) => !item.show || item.show(this.formData))
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    toolbar() {
      const buttons = [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'info'
        }
      ]

      // 根据页面类型和状态显示不同按钮
      if (this.pageType === 'create') {
        // 创建模式：保存、保存并提交
        buttons.push(
          {
            code: 'save',
            name: this.$t('保存'),
            status: 'primary'
          },
          {
            code: 'submit',
            name: this.$t('保存并提交'),
            status: 'primary'
          }
        )
      } else if (this.pageType === 'edit') {
        // 编辑模式：根据状态显示按钮
        const status = this.formData.status

        if (status === '10') {
          // 草稿状态：保存、提交
          buttons.push(
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'primary'
            },
            {
              code: 'submit',
              name: this.$t('提交'),
              status: 'primary'
            }
          )
        }

        if (['20', '40'].includes(status)) {
          // 已提交、已拒绝状态：取消
          buttons.push({
            code: 'cancel',
            name: this.$t('取消'),
            status: 'warning'
          })
        }

        if (status === '40') {
          // 已拒绝状态：重新提交
          buttons.push({
            code: 'submit',
            name: this.$t('重新提交'),
            status: 'primary'
          })
        }
      }

      return buttons
    },
    formRules() {
      return {
        shippingMethod: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        logisticsNo: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        logisticsCompany: [
          {
            required: true,
            message: this.$t('请输入物流公司'),
            trigger: 'blur'
          }
        ],
        senderName: [
          {
            required: true,
            message: this.$t('请输入发件人姓名'),
            trigger: 'blur'
          }
        ],
        senderPhone: [
          {
            required: true,
            message: this.$t('请输入发件人手机号'),
            trigger: 'blur'
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$t('请输入正确的手机号'),
            trigger: 'blur'
          }
        ],
        carModel: [
          {
            required: true,
            message: this.$t('请输入车型'),
            trigger: 'blur'
          }
        ],
        licensePlate: [
          {
            required: true,
            message: this.$t('请输入车牌号'),
            trigger: 'blur'
          },
          { validator: this.validateCarNo, trigger: ['blur', 'change'] }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            message: this.$t('请输入司机手机号'),
            trigger: 'blur'
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$t('请输入正确的手机号'),
            trigger: 'blur'
          }
        ],
        expectedArrivalTime: [
          {
            required: true,
            message: this.$t('请选择预计到货时间'),
            trigger: 'blur'
          }
        ]
      }
    },
    formItems() {
      return [
        {
          label: this.$t('工厂编码'),
          prop: 'siteCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('采购订单号'),
          prop: 'siteName',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('采购订单行号'),
          prop: 'warehouseType',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('加工商编码'),
          prop: 'supplierCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('供应商名称'),
          prop: 'supplierName',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('同步状态'),
          prop: 'deliveryAddress',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('采方备注'),
          prop: 'shipmentRemark',
          component: 'vxe-input',
          props: {
            clearable: true,
            placeholder: this.$t('请输入')
          }
        },
        {
          label: this.$t('供方备注'),
          prop: 'shipmentRemark',
          component: 'vxe-input',
          props: {
            clearable: true,
            placeholder: this.$t('请输入')
          }
        },
        {
          label: this.$t('同步接口信息'),
          prop: 'shipmentRemark',
          component: 'vxe-input',
          props: {
            clearable: true,
            placeholder: this.$t('请输入')
          }
        }
      ]
    },
    tabList() {
      return [
        { title: this.$t('物料信息'), compName: 'itemTab' },
        { title: this.$t('附件'), compName: 'fileTab' }
      ]
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./components/itemTab.vue')
          break
        case 1:
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          break
      }
      return comp
    },
    // 确保数据正确传递给对应的tab
    currentTableData() {
      switch (this.activeTabIndex) {
        case 0:
          return this.itemList
        case 1:
          return this.attachmentList
        default:
          return []
      }
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    /**
     * 初始化页面数据
     */
    async initPage() {
      await this.getDetailInfo()
      let selectedRecords = sessionStorage.getItem('deliveryPlanSelectedRecords')
        ? JSON.parse(sessionStorage.getItem('deliveryPlanSelectedRecords'))
        : []
      if (selectedRecords?.length !== 0) {
        const record = selectedRecords[0]
        this.$set(this.formData, 'siteCode', record.siteCode)
        this.$set(this.formData, 'siteName', record.siteName)
        this.$set(this.formData, 'warehouseType', record.warehouseType || 'VMI')
        this.$set(this.formData, 'warehouseCode', record.warehouseCode)
        this.$set(this.formData, 'warehouseName', record.warehouseName)
        this.$set(this.formData, 'supplierTenantId', record.supplierTenantId)
        this.$set(this.formData, 'supplierCode', record.supplierCode)
        this.$set(this.formData, 'supplierName', record.supplierName)
        this.$set(this.formData, 'shippingMethod', record.shippingMethod || 2)
        this.$set(this.formData, 'purchaseUnshipmentNum', record.purchaseUnshipmentNum)
        this.$set(this.formData, 'shipmentRemark', record.shipmentRemark)
        this.$set(this.formData, 'status', 1)
        if (this.pageType !== 'create') {
          this.$set(this.formData, 'id', record.id)
          this.$set(this.formData, 'stockInCode', record.stockInCode)
          this.$set(this.formData, 'deliveryAddress', record.deliveryAddress)
          this.$set(this.formData, 'shippingMethod', record.shippingMethod)
          this.$set(this.formData, 'logisticsNo', record.logisticsNo)
          this.$set(this.formData, 'logisticsCompany', record.logisticsCompany)
          this.$set(this.formData, 'senderName', record.senderName)
          this.$set(this.formData, 'senderPhone', record.senderPhone)
          this.$set(this.formData, 'carModel', record.carModel)
          this.$set(this.formData, 'licensePlate', record.licensePlate)
          this.$set(this.formData, 'driverName', record.driverName)
          this.$set(this.formData, 'driverPhone', record.driverPhone)
          this.$set(this.formData, 'expectedArrivalTime', record.expectedArrivalTime)
          this.$set(this.formData, 'createUserName', record.createUserName)
          this.$set(this.formData, 'createTime', record.createTime)
          this.$set(this.formData, 'status', record.status)
        }
      }
    },

    async getDetailInfo() {
      if (!this.$route.query?.id) {
        return
      }
      try {
        this.loading = true
        // 获取主单详情
        const res = await this.$API.outsourcingNew.getSubstituteMaterialApplicationMainDetailApi({
          id: this.$route.query.id
        })
        if (res.code === 200) {
          this.formData = res.data || {}
          // 获取物料明细
          await this.getItemList()
          // 获取附件列表
          await this.getAttachmentList()
        } else {
          this.$toast({ content: res.msg || this.$t('获取详情失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$toast({ content: this.$t('获取详情失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取物料明细列表
     */
    async getItemList() {
      try {
        const params = {
          mainIdList: [this.$route.query.id],
          page: {
            current: 1,
            size: 1000 // 获取所有数据
          }
        }
        const res = await this.$API.outsourcingNew.getSubstituteMaterialApplicationItemListApi(
          params
        )
        if (res.code === 200) {
          this.itemList = res.data?.records || []
        }
      } catch (error) {
        console.error('获取物料明细失败:', error)
      }
    },

    /**
     * 获取附件列表
     */
    async getAttachmentList() {
      try {
        const params = {
          docId: this.$route.query.id,
          docType: 'substitute_material'
        }
        const res =
          await this.$API.outsourcingNew.getSubstituteMaterialApplicationAttachmentListApi(params)
        if (res.code === 200) {
          this.attachmentList = res.data || []
        }
      } catch (error) {
        console.error('获取附件列表失败:', error)
      }
    },

    /**
     * 处理标签页切换
     */
    handleTabChange(index) {
      this.$refs.tabsRef.activeTab = index
      this.activeTabIndex = index
    },

    /**
     * 获取组件属性
     */
    getComponentProps(item) {
      const baseProps = {
        disabled: !this.editable || item.props?.disabled
      }

      // 合并基础属性和表单项特定属性
      const props = {
        ...baseProps,
        ...item.props
      }

      // 根据组件类型添加特定属性
      switch (item.component) {
        case 'vxe-input':
          props.type = item.type || 'text'
          break
        case 'vxe-select':
          props.options = item.options || []
          break
        default:
          break
      }

      return props
    },

    /**
     * 处理字段变化
     */
    handleFieldChange(field) {
      // 处理特定字段的联动逻辑
      switch (field) {
        case 'shippingMethod':
          this.$refs.formRef?.clearValidate()
          break
        default:
          break
      }

      // 触发表单验证
      this.$refs.formRef?.validateField(field)
    },

    /**
     * 更新详情数据
     */
    updateDetail(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.itemList = data
          break
        case 1:
          this.attachmentList = data
          break
        default:
          break
      }
    },

    /**
     * 返回上一页
     */
    handleBack() {
      this.$router.go(-1)
    },

    /**
     * 展开/收缩
     */
    handleToggle() {
      this.isExpand = !this.isExpand
    },

    /**
     * 处理工具栏按钮点击
     */
    async handleClickToolBar(item) {
      const actionMap = {
        back: () => this.handleBack(),
        save: () => this.handleSave('save'),
        submit: () => this.handleSubmitAction(),
        cancel: () => this.handleCancelAction()
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: callback
      })
    },

    /**
     * 处理提交操作
     */
    async handleSubmitAction() {
      if (this.pageType === 'create') {
        // 创建模式：保存并提交
        this.showConfirmDialog('保存并提交', async () => {
          const saveResult = await this.handleSave('save')
          if (saveResult !== false) {
            await this.handleSubmit()
          }
        })
      } else {
        // 编辑模式：直接提交
        this.showConfirmDialog('提交', () => this.handleSubmit())
      }
    },

    /**
     * 处理取消操作
     */
    async handleCancelAction() {
      this.showConfirmDialog('取消', () => this.handleCancel())
    },

    /**
     * 提交
     */
    async handleSubmit() {
      try {
        if (!this.formData.id) {
          this.$toast({ content: this.$t('请先保存数据'), type: 'warning' })
          return
        }

        const res = await this.$API.outsourcingNew.batchSubmitSubstituteMaterialApplicationApi({
          ids: [this.formData.id]
        })

        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          // 刷新页面数据
          await this.getDetailInfo()
        } else {
          this.$toast({ content: res.msg || this.$t('提交失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$toast({ content: this.$t('提交失败'), type: 'error' })
      }
    },

    /**
     * 取消
     */
    async handleCancel() {
      try {
        if (!this.formData.id) {
          this.$toast({ content: this.$t('数据不存在'), type: 'warning' })
          return
        }

        const res = await this.$API.outsourcingNew.batchCancelSubstituteMaterialApplicationApi({
          ids: [this.formData.id]
        })

        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          // 刷新页面数据
          await this.getDetailInfo()
        } else {
          this.$toast({ content: res.msg || this.$t('取消失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('取消失败:', error)
        this.$toast({ content: this.$t('取消失败'), type: 'error' })
      }
    },

    /**
     * 保存表单
     */
    async handleSave(type) {
      try {
        // 表单验证
        const valid = await this.validateForm()
        if (!valid) return false

        // 先保存主单
        const res = await this.$API.outsourcingNew.saveSubstituteMaterialApplicationMainApi({
          ...this.formData
        })
        if (res.code === 200) {
          this.formData.id = res.data
        } else {
          this.$toast({ content: res.msg || this.$t('保存主单失败'), type: 'warning' })
          return false
        }

        if (!this.formData.id) {
          return false
        }

        // 保存物料明细
        if (this.itemList.length > 0) {
          const saveRes =
            await this.$API.outsourcingNew.saveSubstituteMaterialApplicationItemListApi({
              mainId: this.formData.id,
              itemList: this.itemList.map((item) => ({
                ...item,
                id: item.id.includes('row_') ? null : item.id,
                demandDate: item.demandDate ? new Date(item.demandDate).getTime() : null
              }))
            })
          if (saveRes.code !== 200) {
            this.$toast({ content: saveRes.msg || this.$t('保存物料明细失败'), type: 'warning' })
            return false
          }
        }

        // 保存附件信息
        if (this.attachmentList.length > 0) {
          const attachmentSaveRes = await this.$API.substituteMaterial
            .saveAttachmentList({
              docId: this.formData.id,
              docType: 'substitute_material',
              attachmentList: this.attachmentList.map((item) => ({
                ...item,
                id: item.id.includes('attachment_') ? null : item.id
              }))
            })
            .catch(() => {
              this.$toast({ content: this.$t('保存附件失败'), type: 'warning' })
              return { code: 500 }
            })

          if (attachmentSaveRes.code !== 200) {
            this.$toast({ content: this.$t('保存附件失败'), type: 'warning' })
            return false
          }
        }

        this.$toast({ content: this.$t('保存成功'), type: 'success' })

        if (type === 'save') {
          this.$router.push({
            path: '/purchase-pv/supplier-vmi-management/inventory-management-detail',
            query: {
              type: 'edit',
              source: this.$route.query?.source,
              id: this.formData.id,
              timeStamp: Date.now()
            }
          })
        }

        return true
      } catch (error) {
        console.error('保存失败:', error)
        return false
      }
    },

    /**
     * 表单验证
     */
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.formRef.validate(async (valid) => {
          if (!valid) {
            this.$toast({ content: this.$t('请完善必填信息'), type: 'warning' })
            resolve(false)
            return
          }

          // 验证物料明细
          if (!this.itemList || this.itemList.length === 0) {
            this.$toast({ content: this.$t('请添加物料明细'), type: 'warning' })
            // 跳转到物料明细tab
            this.handleTabChange(0)
            resolve(false)
            return
          }

          resolve(true)
        })
      })
    },

    /**
     * 获取请求参数
     */
    getParams() {
      let params = {
        ...this.formData,
        ...this.driverForm,
        addOrUpdateDtoList: this.itemList,
        snList: this.snList
      }
      return params
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('deliveryPlanSelectedRecords')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-title {
        display: flex;
        padding-top: 10px;
        span {
          margin-right: 35px;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
      cursor: pointer;
      &:hover {
        background-color: #5a657b;
      }
    }
  }
}
.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}
.body-container {
  height: calc(100% - 80px);
}
::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
