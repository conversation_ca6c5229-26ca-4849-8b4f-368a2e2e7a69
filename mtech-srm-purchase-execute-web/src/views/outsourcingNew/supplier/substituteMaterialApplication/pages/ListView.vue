<!-- 供方-替代料申请管理-列表视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="efb8c73d-8a80-445f-8b61-611a9f4e5bcb"
      search-grid-id="sc7bc8ab9-91af-4386-9367-21a211d011a4"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getListSearchFormItems } from '../config/searchForm'
import { listColumnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: listColumnData,
      toolbar,
      currentPage: 1,
      pageSize: 50
    }
  },
  computed: {
    searchConditions() {
      const items = getListSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm, page, pageSize }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = page || 1
      this.pageSize = pageSize || 50
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.currentPage = 1
      this.getTableData()
    },
    handlePageChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    handlePageSizeChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    async getTableData() {
      // 构建查询参数，根据接口文档格式
      const params = {
        // 分页参数
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        // 根据接口文档映射搜索条件
        siteCodeList: this.searchForm.siteCodeList || [],
        replaceItemCode: this.searchForm.replaceItemCode || '',
        statusList: this.searchForm.statusList || [],
        orderCode: this.searchForm.orderCode || '',
        orderLineNo: this.searchForm.orderLineNo || '',
        syncSapStatusList: this.searchForm.syncSapStatusList || [],
        syncSapMsg: this.searchForm.syncSapMsg || ''
      }

      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getSubstituteMaterialApplicationSupplierListApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        add: () => this.handleAdd(),
        edit: () => this.handleEdit(),
        delete: () => this.handleDelete(item),
        submit: () => this.handleSubmit(item),
        cancel: () => this.handleCancel(item),
        import: () => this.handleImport(item),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    handleAdd() {
      this.$router.push({
        path: '/purchase-execute/substitute-material-application-detail-sup',
        query: {
          type: 'create',
          timeStamp: new Date().getTime()
        }
      })
    },

    /**
     * 编辑
     */
    handleEdit() {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要编辑的记录'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一条记录进行编辑'), type: 'warning' })
        return
      }

      const record = selectedRecords[0]
      // 只有草稿状态的数据可以编辑
      if (record.status !== '10') {
        this.$toast({ content: this.$t('只有草稿状态的数据可以编辑'), type: 'warning' })
        return
      }

      this.$router.push({
        path: '/purchase-execute/substitute-material-application-detail-sup',
        query: {
          type: 'edit',
          id: record.id,
          timeStamp: new Date().getTime()
        }
      })
    },

    /**
     * 删除 - 支持批量删除，仅支持新建状态的数据，物理删除
     */
    async handleDelete(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要删除的记录'), type: 'warning' })
        return
      }

      // 检查状态：仅支持新建状态(草稿状态10)的数据
      const invalidRecords = selectedRecords.filter((record) => record.status !== '10')
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能删除草稿状态的数据'),
          type: 'warning'
        })
        return
      }

      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除以下记录吗？\n{0}', [recordNames])
        },
        success: async () => {
          try {
            item.loading = true
            const ids = selectedRecords.map((record) => record.id)
            const res = await this.$API.outsourcingNew.batchDeleteSubstituteMaterialApplicationApi({
              ids
            })

            if (res.code === 200) {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.getTableData() // 刷新列表
            } else {
              this.$toast({ content: res.msg || this.$t('删除失败'), type: 'warning' })
            }
          } catch (error) {
            console.error('删除失败:', error)
            this.$toast({ content: this.$t('删除失败'), type: 'error' })
          } finally {
            item.loading = false
          }
        }
      })
    },

    /**
     * 提交 - 支持批量提交，支持提交新建、采购已拒绝的数据，提交后状态变为待采购确认
     */
    async handleSubmit(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要提交的记录'), type: 'warning' })
        return
      }

      // 检查状态：支持提交草稿(10)、已拒绝(40)的数据
      const validStatuses = ['10', '40']
      const invalidRecords = selectedRecords.filter(
        (record) => !validStatuses.includes(record.status)
      )
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能提交草稿或已拒绝状态的数据'),
          type: 'warning'
        })
        return
      }

      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交以下记录吗？\n{0}', [recordNames])
        },
        success: async () => {
          try {
            item.loading = true
            const ids = selectedRecords.map((record) => record.id)
            const res = await this.$API.outsourcingNew.batchSubmitSubstituteMaterialApplicationApi({
              ids
            })

            if (res.code === 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.getTableData() // 刷新列表
            } else {
              this.$toast({ content: res.msg || this.$t('提交失败'), type: 'warning' })
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$toast({ content: this.$t('提交失败'), type: 'error' })
          } finally {
            item.loading = false
          }
        }
      })
    },

    /**
     * 取消 - 支持批量取消，支持取消待采购确认、采购已拒绝的数据
     */
    async handleCancel(item) {
      const selectedRecords = this.$refs.commonListRef.getSelectedRows()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要取消的记录'), type: 'warning' })
        return
      }

      // 检查状态：支持取消已提交(20)、已拒绝(40)的数据
      const validStatuses = ['20', '40']
      const invalidRecords = selectedRecords.filter(
        (record) => !validStatuses.includes(record.status)
      )
      if (invalidRecords.length > 0) {
        this.$toast({
          content: this.$t('只能取消已提交或已拒绝状态的数据'),
          type: 'warning'
        })
        return
      }

      const recordNames = selectedRecords.map((record) => record.replaceItemCode).join('、')
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认取消以下记录吗？\n{0}', [recordNames])
        },
        success: async () => {
          try {
            item.loading = true
            const ids = selectedRecords.map((record) => record.id)
            const res = await this.$API.outsourcingNew.batchCancelSubstituteMaterialApplicationApi({
              ids
            })

            if (res.code === 200) {
              this.$toast({ content: this.$t('取消成功'), type: 'success' })
              this.getTableData() // 刷新列表
            } else {
              this.$toast({ content: res.msg || this.$t('取消失败'), type: 'warning' })
            }
          } catch (error) {
            console.error('取消失败:', error)
            this.$toast({ content: this.$t('取消失败'), type: 'error' })
          } finally {
            item.loading = false
          }
        }
      })
    },

    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.outsourcingNew.importSubstituteMaterialApplicationApi,
          downloadTemplateApi:
            this.$API.outsourcingNew.downloadSubstituteMaterialApplicationTemplateApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res =
          await this.$API.outsourcingNew.exportSubstituteMaterialApplicationSupplierListApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
