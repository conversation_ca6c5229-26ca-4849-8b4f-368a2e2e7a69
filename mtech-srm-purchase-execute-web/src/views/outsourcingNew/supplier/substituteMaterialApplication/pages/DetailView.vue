<!-- 供方-替代料申请管理-明细视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="d185c9d8-1fc0-4c63-9ec0-43821e289672"
      search-grid-id="b518813d-30e8-49e1-9274-39ea1a88d090"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getDetailSearchFormItems } from '../config/searchForm'
import { detailColumnData, detailToolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: detailColumnData,
      toolbar: detailToolbar,
      currentPage: 1,
      pageSize: 50
    }
  },
  computed: {
    searchConditions() {
      const items = getDetailSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm, page, pageSize }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.currentPage = page || 1
      this.pageSize = pageSize || 50
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.currentPage = 1
      this.getTableData()
    },
    handlePageChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    handlePageSizeChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    async getTableData() {
      // 构建查询参数，根据明细接口文档格式
      const params = {
        // 分页参数
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        // 根据接口文档映射搜索条件
        siteCodeList: this.searchForm.siteCodeList || [],
        replaceItemCode: this.searchForm.replaceItemCode || '',
        statusList: this.searchForm.statusList || [],
        orderCode: this.searchForm.orderCode || '',
        orderLineNo: this.searchForm.orderLineNo ? Number(this.searchForm.orderLineNo) : null,
        itemCode: this.searchForm.itemCode || '',
        itemName: this.searchForm.itemName || '',
        buyerOrgCodeList: this.searchForm.buyerOrgCodeList || [],
        buyerOrgName: this.searchForm.buyerOrgName || '',
        categoryCode: this.searchForm.categoryCode || '',
        categoryName: this.searchForm.categoryName || '',
        warehouse: this.searchForm.warehouse || '',
        syncSapStatusList: this.searchForm.syncSapStatusList || [],
        syncSapMsg: this.searchForm.syncSapMsg || '',
        // 需求时间范围
        demandTimeS: this.searchForm.demandTime?.[0]
          ? new Date(this.searchForm.demandTime[0]).getTime()
          : null,
        demandTimeE: this.searchForm.demandTime?.[1]
          ? new Date(this.searchForm.demandTime[1]).getTime()
          : null
      }

      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getSubstituteMaterialApplicationSupplierDetailApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        const total = res.data?.total || 0
        this.$refs.commonListRef.setTableData(records)
        this.$refs.commonListRef.setPagination({
          total,
          current: this.currentPage,
          size: this.pageSize
        })
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res =
          await this.$API.outsourcingNew.exportSubstituteMaterialApplicationSupplierDetailApi(
            params
          )
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
