/**
 * 加工商结存库存查询搜索表单配置
 */
import { i18n } from '@/main.js'

export const getSearchFormItems = () => [
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('供应商编码'),
    field: 'supplierCode',
    type: 'input'
  },
  {
    label: i18n.t('物料编码'),
    field: 'itemCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('年份'),
    field: 'year',
    type: 'select',
    options: (() => {
      const currentYear = new Date().getFullYear()
      const years = []
      for (let i = currentYear - 20; i <= currentYear + 20; i++) {
        years.push({ text: i.toString(), value: i.toString() })
      }
      return years
    })()
  },
  {
    label: i18n.t('月份'),
    field: 'month',
    type: 'select',
    options: [
      { text: '1', value: '1' },
      { text: '2', value: '2' },
      { text: '3', value: '3' },
      { text: '4', value: '4' },
      { text: '5', value: '5' },
      { text: '6', value: '6' },
      { text: '7', value: '7' },
      { text: '8', value: '8' },
      { text: '9', value: '9' },
      { text: '10', value: '10' },
      { text: '11', value: '11' },
      { text: '12', value: '12' }
    ]
  }
]
